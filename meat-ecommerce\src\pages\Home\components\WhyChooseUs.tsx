import React from 'react';
import { motion } from 'framer-motion';
import { H2, H3, Body } from '../../../components/Typography';
import { whyChooseUsData } from '../../../data/homeData';

const WhyChooseUs: React.FC = () => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <H2 className="mb-4">Why Choose Us?</H2>
          <Body className="text-gray-600 max-w-2xl mx-auto">
            We're committed to providing you with the highest quality meat and exceptional service
          </Body>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {whyChooseUsData.map((feature, index) => (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center group"
            >
              {/* Icon */}
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.2 }}
                className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary/10 to-primary/20 rounded-2xl flex items-center justify-center group-hover:from-primary/20 group-hover:to-primary/30 transition-all duration-300"
              >
                <span className="text-4xl">{feature.icon}</span>
              </motion.div>

              {/* Title */}
              <H3 className="mb-3 group-hover:text-primary transition-colors duration-200">
                {feature.title}
              </H3>

              {/* Description */}
              <Body className="text-gray-600 leading-relaxed">
                {feature.description}
              </Body>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-16 bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8"
        >
          <H3 className="mb-4">Ready to experience the difference?</H3>
          <Body className="text-gray-600 mb-6 max-w-xl mx-auto">
            Join thousands of satisfied customers who trust us for their daily meat needs
          </Body>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-primary text-white px-8 py-3 rounded-full font-semibold hover:bg-primary/90 transition-colors duration-200 shadow-medium"
          >
            Start Shopping Now
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
