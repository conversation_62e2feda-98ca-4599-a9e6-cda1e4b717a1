import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>R<PERSON>, Star, Clock, Truck, Shield, Award, Users, Zap } from 'lucide-react';
import { H1, Body } from '../../../components/Typography';

const HeroBanner: React.FC = () => {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-primary/5 via-white to-accent/5 overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0]
          }}
          transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
          className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            y: [-20, 20, -20],
            x: [-10, 10, -10]
          }}
          transition={{ duration: 15, repeat: Infinity, ease: "easeInOut" }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-r from-secondary/5 to-primary/5 rounded-full blur-3xl"
        />
      </div>

      <div className="relative container mx-auto px-4 py-16 lg:py-24">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            {/* Premium Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center gap-3 bg-gradient-to-r from-primary/10 to-accent/10 text-primary px-6 py-3 rounded-full text-sm font-semibold border border-primary/20 backdrop-blur-sm"
            >
              <Award className="w-5 h-5 fill-current" />
              Premium Quality Since 2020
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-2 h-2 bg-primary rounded-full"
              />
            </motion.div>

            {/* Main Heading */}
            <div className="space-y-6">
              <H1 className="text-5xl lg:text-7xl font-bold leading-tight">
                <span className="text-secondary">Fresh</span>{' '}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
                  Premium
                </span>
                <br />
                <span className="text-secondary">Meat Delivered</span>
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-accent to-primary">
                  To Your Door
                </span>
              </H1>

              <Body className="text-xl text-gray-600 max-w-xl leading-relaxed">
                Experience the finest selection of fresh, premium quality meat sourced directly from trusted farms.
                Fast delivery, guaranteed freshness, and unmatched taste that brings restaurant-quality meals to your home.
              </Body>
            </div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="grid grid-cols-2 lg:grid-cols-4 gap-4"
            >
              <div className="flex items-center gap-2 text-sm text-gray-600 bg-white/50 backdrop-blur-sm p-3 rounded-lg border border-gray-100">
                <Clock className="w-5 h-5 text-primary" />
                <span className="font-medium">30-min delivery</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 bg-white/50 backdrop-blur-sm p-3 rounded-lg border border-gray-100">
                <Truck className="w-5 h-5 text-primary" />
                <span className="font-medium">Free shipping ₹999+</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 bg-white/50 backdrop-blur-sm p-3 rounded-lg border border-gray-100">
                <Shield className="w-5 h-5 text-primary" />
                <span className="font-medium">100% Fresh</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 bg-white/50 backdrop-blur-sm p-3 rounded-lg border border-gray-100">
                <Star className="w-5 h-5 text-primary fill-current" />
                <span className="font-medium">4.9★ rating</span>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Link
                to="/products"
                className="group inline-flex items-center justify-center gap-3 bg-gradient-to-r from-primary to-primary/90 text-white px-10 py-5 rounded-2xl font-bold text-lg hover:from-primary/90 hover:to-primary transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 hover:scale-105"
              >
                <Zap className="w-6 h-6 group-hover:rotate-12 transition-transform" />
                <span>Shop Premium Meat</span>
                <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
              </Link>

              <Link
                to="/about"
                className="group inline-flex items-center justify-center gap-3 bg-white/80 backdrop-blur-sm text-secondary px-10 py-5 rounded-2xl font-bold text-lg border-2 border-gray-200 hover:border-primary hover:text-primary hover:bg-white transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <Users className="w-6 h-6 group-hover:scale-110 transition-transform" />
                <span>Our Story</span>
              </Link>
            </motion.div>

            {/* Enhanced Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
              className="grid grid-cols-3 gap-8 pt-8 border-t border-gray-200/50"
            >
              <div className="text-center group">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent"
                >
                  50K+
                </motion.div>
                <div className="text-sm text-gray-600 font-medium">Happy Customers</div>
              </div>
              <div className="text-center group">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent"
                >
                  500+
                </motion.div>
                <div className="text-sm text-gray-600 font-medium">Premium Products</div>
              </div>
              <div className="text-center group">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent"
                >
                  24/7
                </motion.div>
                <div className="text-sm text-gray-600 font-medium">Expert Support</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Content - Enhanced Hero Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="relative z-10">
              <motion.div
                animate={{
                  y: [0, -15, 0],
                  rotate: [0, 1, 0]
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-3xl blur-2xl transform rotate-6 group-hover:rotate-12 transition-transform duration-500"></div>
                <img
                  src="https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                  alt="Premium Meat Selection"
                  className="relative w-full h-96 lg:h-[500px] object-cover rounded-3xl shadow-2xl border-4 border-white/50 backdrop-blur-sm group-hover:scale-105 transition-transform duration-500"
                  onError={(e) => {
                    const target = e.currentTarget;
                    target.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
                      <svg width="500" height="400" viewBox="0 0 500 400" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                          <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#F3F4F6"/>
                            <stop offset="100%" style="stop-color:#E5E7EB"/>
                          </linearGradient>
                        </defs>
                        <rect width="500" height="400" fill="url(#bg)"/>
                        <circle cx="250" cy="200" r="60" fill="#E62E04" opacity="0.8"/>
                        <text x="250" y="280" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#666" text-anchor="middle">🥩 Premium Meat Selection</text>
                        <text x="250" y="300" font-family="Arial, sans-serif" font-size="14" fill="#999" text-anchor="middle">Fresh • Quality • Delivered</text>
                      </svg>
                    `)}`;
                  }}
                />

                {/* Enhanced Floating Cards */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.8, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  transition={{ delay: 1, duration: 0.6 }}
                  className="absolute -top-6 -left-6 bg-white/90 backdrop-blur-md p-5 rounded-2xl shadow-xl border border-white/50"
                >
                  <div className="flex items-center gap-3">
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="w-4 h-4 bg-green-500 rounded-full shadow-lg"
                    />
                    <div>
                      <div className="text-sm font-bold text-gray-800">Fresh Today</div>
                      <div className="text-xs text-gray-600">Just arrived</div>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, scale: 0.8, y: -20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  transition={{ delay: 1.2, duration: 0.6 }}
                  className="absolute -bottom-6 -right-6 bg-gradient-to-r from-primary to-accent text-white p-5 rounded-2xl shadow-xl"
                >
                  <div className="text-center">
                    <div className="text-2xl font-bold">₹299</div>
                    <div className="text-sm opacity-90">per kg</div>
                    <div className="text-xs opacity-75">Best Price</div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, scale: 0.8, x: 20 }}
                  animate={{ opacity: 1, scale: 1, x: 0 }}
                  transition={{ delay: 1.4, duration: 0.6 }}
                  className="absolute top-1/2 -right-4 bg-white/90 backdrop-blur-md p-4 rounded-xl shadow-lg border border-white/50"
                >
                  <div className="flex items-center gap-2">
                    <Star className="w-5 h-5 text-yellow-500 fill-current" />
                    <div>
                      <div className="text-sm font-bold text-gray-800">4.9</div>
                      <div className="text-xs text-gray-600">Rating</div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </div>

            {/* Enhanced Background Decorations */}
            <div className="absolute inset-0 -z-10">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
                className="absolute top-16 right-16 w-24 h-24 bg-gradient-to-r from-accent/20 to-primary/20 rounded-full blur-2xl"
              />
              <motion.div
                animate={{ rotate: [360, 0] }}
                transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                className="absolute bottom-16 left-16 w-20 h-20 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-2xl"
              />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Enhanced Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" fill="none" className="w-full h-24">
          <defs>
            <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="white" />
              <stop offset="50%" stopColor="#fefefe" />
              <stop offset="100%" stopColor="white" />
            </linearGradient>
          </defs>
          <path
            d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z"
            fill="url(#wave-gradient)"
          />
        </svg>
      </div>
    </section>
  );
};

export default HeroBanner;
