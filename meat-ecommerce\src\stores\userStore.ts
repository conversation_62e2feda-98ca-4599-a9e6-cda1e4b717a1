import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { User, Address } from '../types/index';

interface UserStore {
  // State
  user: User | null;
  isAuthenticated: boolean;
  addresses: Address[];
  
  // Actions
  login: (user: User) => void;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  addAddress: (address: Address) => void;
  updateAddress: (addressId: string, addressData: Partial<Address>) => void;
  removeAddress: (addressId: string) => void;
  setDefaultAddress: (addressId: string) => void;
  getDefaultAddress: () => Address | null;
}

export const useUserStore = create<UserStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      addresses: [],

      login: (user: User) => {
        set({
          user,
          isAuthenticated: true,
        });
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          addresses: [],
        });
      },

      updateUser: (userData: Partial<User>) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...userData } : null,
        }));
      },

      addAddress: (address: Address) => {
        set((state) => {
          // If this is the first address, make it default
          const isFirstAddress = state.addresses.length === 0;
          const newAddress = { ...address, isDefault: isFirstAddress };
          
          return {
            addresses: [...state.addresses, newAddress],
          };
        });
      },

      updateAddress: (addressId: string, addressData: Partial<Address>) => {
        set((state) => ({
          addresses: state.addresses.map(address =>
            address.id === addressId
              ? { ...address, ...addressData }
              : address
          ),
        }));
      },

      removeAddress: (addressId: string) => {
        set((state) => {
          const updatedAddresses = state.addresses.filter(
            address => address.id !== addressId
          );
          
          // If we removed the default address, make the first remaining address default
          const removedAddress = state.addresses.find(addr => addr.id === addressId);
          if (removedAddress?.isDefault && updatedAddresses.length > 0) {
            updatedAddresses[0].isDefault = true;
          }
          
          return {
            addresses: updatedAddresses,
          };
        });
      },

      setDefaultAddress: (addressId: string) => {
        set((state) => ({
          addresses: state.addresses.map(address => ({
            ...address,
            isDefault: address.id === addressId,
          })),
        }));
      },

      getDefaultAddress: () => {
        const { addresses } = get();
        return addresses.find(address => address.isDefault) || null;
      },
    }),
    {
      name: 'user-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        addresses: state.addresses,
      }),
    }
  )
);
