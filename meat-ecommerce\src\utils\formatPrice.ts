/**
 * Utility function to format prices consistently in Indian Rupees (INR)
 * across the entire application
 */
export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

/**
 * Format price with decimal places when needed
 */
export const formatPriceWithDecimals = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(price);
};

/**
 * Format price without currency symbol (just the number with commas)
 */
export const formatPriceNumber = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

/**
 * Get currency symbol for INR
 */
export const getCurrencySymbol = (): string => {
  return '₹';
};

/**
 * Calculate discount amount
 */
export const calculateDiscount = (originalPrice: number, currentPrice: number): number => {
  return originalPrice - currentPrice;
};

/**
 * Calculate discount percentage
 */
export const calculateDiscountPercentage = (originalPrice: number, currentPrice: number): number => {
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
};

/**
 * Format discount savings
 */
export const formatSavings = (originalPrice: number, currentPrice: number): string => {
  const savings = calculateDiscount(originalPrice, currentPrice);
  return `Save ${formatPrice(savings)}`;
};
