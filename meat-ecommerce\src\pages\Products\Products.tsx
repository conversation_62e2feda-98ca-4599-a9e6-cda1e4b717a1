import React, { useState, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { Grid, List, Filter, SlidersHorizontal } from 'lucide-react';
import { H1, H2 } from '../../components/Typography';
import ProductCard from '../../components/ProductCard';
import ProductFilters from '../../components/ProductFilters';
import { products, categories } from '../../data/productsData';


const Products: React.FC = () => {
  const { category } = useParams<{ category?: string }>();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'rating'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({});

  // Filter products by category and active filters
  const filteredProducts = useMemo(() => {
    let filtered = products;

    // Filter by category if specified
    if (category) {
      filtered = filtered.filter(product => product.category === category);
    }

    // Apply active filters
    if (activeFilters.category && activeFilters.category.length > 0) {
      filtered = filtered.filter(product =>
        activeFilters.category.includes(product.category)
      );
    }

    if (activeFilters.rating && activeFilters.rating.length > 0) {
      filtered = filtered.filter(product => {
        return activeFilters.rating.some((rating: string) => {
          const minRating = parseInt(rating.replace('+', ''));
          return product.rating >= minRating;
        });
      });
    }

    if (activeFilters.priceRange) {
      const { min, max } = activeFilters.priceRange;
      filtered = filtered.filter(product =>
        product.price >= min && product.price <= max
      );
    }

    // Sort products
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'price':
          comparison = a.price - b.price;
          break;
        case 'rating':
          comparison = a.rating - b.rating;
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [category, sortBy, sortOrder, activeFilters]);

  // Get category info
  const categoryInfo = categories.find(cat => cat.slug === category);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-6">
          {categoryInfo ? (
            <div>
              <H1 className="mb-2">{categoryInfo.name}</H1>
              <p className="text-gray-600">{categoryInfo.description}</p>
            </div>
          ) : (
            <H1>All Products</H1>
          )}
        </div>

        <div className="flex gap-6">
          {/* Filters Sidebar - Desktop */}
          <div className="hidden lg:block w-80 flex-shrink-0">
            <ProductFilters
              isOpen={true}
              onClose={() => {}}
              onFiltersChange={setActiveFilters}
              className="sticky top-6"
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Controls Bar */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 bg-white p-4 rounded-xl shadow-sm">
              <div className="flex items-center gap-4">
                {/* Mobile Filter Button */}
                <button
                  onClick={() => setIsFiltersOpen(true)}
                  className="lg:hidden flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200"
                >
                  <Filter className="w-4 h-4" />
                  <span>Filters</span>
                </button>

                <span className="text-sm text-gray-600">
                  {filteredProducts.length} products found
                </span>
              </div>

              <div className="flex items-center gap-4">
                {/* Sort Controls */}
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [newSortBy, newSortOrder] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder];
                    setSortBy(newSortBy);
                    setSortOrder(newSortOrder);
                  }}
                  className="text-sm border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                >
                  <option value="name-asc">Name (A-Z)</option>
                  <option value="name-desc">Name (Z-A)</option>
                  <option value="price-asc">Price (Low to High)</option>
                  <option value="price-desc">Price (High to Low)</option>
                  <option value="rating-desc">Rating (High to Low)</option>
                  <option value="rating-asc">Rating (Low to High)</option>
                </select>

                {/* View Mode Toggle */}
                <div className="flex items-center border border-gray-200 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 transition-colors duration-200 ${
                      viewMode === 'grid'
                        ? 'bg-primary text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 transition-colors duration-200 ${
                      viewMode === 'list'
                        ? 'bg-primary text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Products Grid */}
            <div className={`grid gap-4 ${
              viewMode === 'grid'
                ? 'grid-cols-1 sm:grid-cols-2 xl:grid-cols-3'
                : 'grid-cols-1'
            }`}>
              {filteredProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  variant={viewMode === 'list' ? 'featured' : 'default'}
                />
              ))}
            </div>

            {/* Empty State */}
            {filteredProducts.length === 0 && (
              <div className="text-center py-12">
                <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <SlidersHorizontal className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No products found</h3>
                <p className="text-gray-500">Try adjusting your filters or search criteria</p>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Filters */}
        <ProductFilters
          isOpen={isFiltersOpen}
          onClose={() => setIsFiltersOpen(false)}
          onFiltersChange={setActiveFilters}
        />
      </div>
    </div>
  );
};

export default Products;
