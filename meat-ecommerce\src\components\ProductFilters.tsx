import React, { useState } from 'react';
import { Filter, X, ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '../utils/cn';

interface FilterOption {
  id: string;
  label: string;
  count?: number;
}

interface FilterSection {
  id: string;
  title: string;
  options: FilterOption[];
  type: 'checkbox' | 'radio' | 'range';
}

interface ProductFiltersProps {
  isOpen: boolean;
  onClose: () => void;
  onFiltersChange: (filters: Record<string, any>) => void;
  className?: string;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  isOpen,
  onClose,
  onFiltersChange,
  className
}) => {
  const [selectedFilters, setSelectedFilters] = useState<Record<string, any>>({});
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['category', 'price']));
  const [priceRange, setPriceRange] = useState({ min: 0, max: 2000 });

  const filterSections: FilterSection[] = [
    {
      id: 'category',
      title: 'Category',
      type: 'checkbox',
      options: [
        { id: 'chicken', label: 'Chicken', count: 24 },
        { id: 'mutton', label: 'Mutton', count: 18 },
        { id: 'fish', label: 'Fish', count: 15 },
        { id: 'prawns', label: 'Prawns', count: 12 },
        { id: 'eggs', label: 'Eggs', count: 8 }
      ]
    },
    {
      id: 'rating',
      title: 'Rating',
      type: 'checkbox',
      options: [
        { id: '4+', label: '4★ & above', count: 45 },
        { id: '3+', label: '3★ & above', count: 62 },
        { id: '2+', label: '2★ & above', count: 71 }
      ]
    },
    {
      id: 'weight',
      title: 'Weight',
      type: 'checkbox',
      options: [
        { id: '250g', label: '250g', count: 15 },
        { id: '500g', label: '500g', count: 28 },
        { id: '1kg', label: '1kg', count: 35 },
        { id: '2kg', label: '2kg+', count: 12 }
      ]
    },
    {
      id: 'freshness',
      title: 'Freshness',
      type: 'radio',
      options: [
        { id: 'today', label: 'Fresh Today', count: 42 },
        { id: 'yesterday', label: 'Yesterday', count: 18 },
        { id: 'week', label: 'This Week', count: 25 }
      ]
    }
  ];

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const handleFilterChange = (sectionId: string, optionId: string, checked: boolean) => {
    const section = filterSections.find(s => s.id === sectionId);
    if (!section) return;

    let newFilters = { ...selectedFilters };

    if (section.type === 'radio') {
      newFilters[sectionId] = checked ? optionId : null;
    } else {
      if (!newFilters[sectionId]) {
        newFilters[sectionId] = [];
      }
      if (checked) {
        newFilters[sectionId] = [...newFilters[sectionId], optionId];
      } else {
        newFilters[sectionId] = newFilters[sectionId].filter((id: string) => id !== optionId);
      }
    }

    setSelectedFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handlePriceRangeChange = (min: number, max: number) => {
    setPriceRange({ min, max });
    const newFilters = { ...selectedFilters, priceRange: { min, max } };
    setSelectedFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    setSelectedFilters({});
    setPriceRange({ min: 0, max: 2000 });
    onFiltersChange({});
  };

  const getActiveFilterCount = () => {
    let count = 0;
    Object.values(selectedFilters).forEach(value => {
      if (Array.isArray(value)) {
        count += value.length;
      } else if (value !== null && value !== undefined) {
        count += 1;
      }
    });
    return count;
  };

  return (
    <>
      {/* Mobile Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Filter Sidebar */}
      <div
        className={cn(
          "fixed lg:sticky top-0 left-0 h-full lg:h-auto w-80 lg:w-full bg-white shadow-xl lg:shadow-none z-50 lg:z-auto transform transition-transform duration-300 ease-in-out overflow-y-auto",
          isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
          className
        )}
      >
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Filter className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-bold text-gray-800">Filters</h2>
              {getActiveFilterCount() > 0 && (
                <span className="bg-primary text-white text-xs px-2 py-1 rounded-full">
                  {getActiveFilterCount()}
                </span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={clearAllFilters}
                className="text-sm text-gray-500 hover:text-primary transition-colors duration-200"
              >
                Clear All
              </button>
              <button
                onClick={onClose}
                className="lg:hidden w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200"
              >
                <X className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Price Range */}
          <div className="mb-6">
            <button
              onClick={() => toggleSection('price')}
              className="flex items-center justify-between w-full py-3 text-left"
            >
              <span className="font-semibold text-gray-800">Price Range</span>
              {expandedSections.has('price') ? (
                <ChevronUp className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-500" />
              )}
            </button>
            
            {expandedSections.has('price') && (
              <div className="mt-3 space-y-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <label className="block text-xs text-gray-500 mb-1">Min</label>
                    <input
                      type="number"
                      value={priceRange.min}
                      onChange={(e) => handlePriceRangeChange(parseInt(e.target.value) || 0, priceRange.max)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                      placeholder="₹0"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-xs text-gray-500 mb-1">Max</label>
                    <input
                      type="number"
                      value={priceRange.max}
                      onChange={(e) => handlePriceRangeChange(priceRange.min, parseInt(e.target.value) || 2000)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                      placeholder="₹2000"
                    />
                  </div>
                </div>
                
                {/* Quick Price Buttons */}
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { label: 'Under ₹200', min: 0, max: 200 },
                    { label: '₹200-500', min: 200, max: 500 },
                    { label: '₹500-1000', min: 500, max: 1000 },
                    { label: 'Above ₹1000', min: 1000, max: 2000 }
                  ].map((range) => (
                    <button
                      key={range.label}
                      onClick={() => handlePriceRangeChange(range.min, range.max)}
                      className={cn(
                        "px-3 py-2 text-xs rounded-lg border transition-colors duration-200",
                        priceRange.min === range.min && priceRange.max === range.max
                          ? "bg-primary text-white border-primary"
                          : "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100"
                      )}
                    >
                      {range.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Filter Sections */}
          {filterSections.map((section) => (
            <div key={section.id} className="mb-6">
              <button
                onClick={() => toggleSection(section.id)}
                className="flex items-center justify-between w-full py-3 text-left"
              >
                <span className="font-semibold text-gray-800">{section.title}</span>
                {expandedSections.has(section.id) ? (
                  <ChevronUp className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                )}
              </button>
              
              {expandedSections.has(section.id) && (
                <div className="mt-3 space-y-3">
                  {section.options.map((option) => {
                    const isChecked = section.type === 'radio'
                      ? selectedFilters[section.id] === option.id
                      : selectedFilters[section.id]?.includes(option.id);

                    return (
                      <label
                        key={option.id}
                        className="flex items-center gap-3 cursor-pointer group"
                      >
                        <input
                          type={section.type === 'radio' ? 'radio' : 'checkbox'}
                          name={section.type === 'radio' ? section.id : undefined}
                          checked={isChecked || false}
                          onChange={(e) => handleFilterChange(section.id, option.id, e.target.checked)}
                          className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary/20"
                        />
                        <span className="flex-1 text-sm text-gray-700 group-hover:text-gray-900">
                          {option.label}
                        </span>
                        {option.count && (
                          <span className="text-xs text-gray-500">({option.count})</span>
                        )}
                      </label>
                    );
                  })}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default ProductFilters;
