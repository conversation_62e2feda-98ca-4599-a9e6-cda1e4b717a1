import React from 'react';
import { motion } from 'framer-motion';
import { Phone, Mail, MapPin, Clock } from 'lucide-react';
import { H1, H2, Body } from '../components/Typography';

const Contact: React.FC = () => {
  return (
    <div className="min-h-screen bg-background-light">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-soft p-8"
        >
          <H1 className="mb-8 text-center">Contact Us</H1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <Phone className="w-6 h-6 text-primary mt-1" />
                <div>
                  <H2 className="mb-2">Phone</H2>
                  <Body className="text-gray-600">+91 98765 43210</Body>
                  <Body className="text-gray-600">+91 87654 32109</Body>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Mail className="w-6 h-6 text-primary mt-1" />
                <div>
                  <H2 className="mb-2">Email</H2>
                  <Body className="text-gray-600"><EMAIL></Body>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <MapPin className="w-6 h-6 text-primary mt-1" />
                <div>
                  <H2 className="mb-2">Address</H2>
                  <Body className="text-gray-600">
                    123 Meat Street, Fresh Market,<br />
                    Mumbai, Maharashtra 400001
                  </Body>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Clock className="w-6 h-6 text-primary mt-1" />
                <div>
                  <H2 className="mb-2">Hours</H2>
                  <Body className="text-gray-600">Mon - Sat: 8:00 AM - 10:00 PM</Body>
                  <Body className="text-gray-600">Sunday: 9:00 AM - 9:00 PM</Body>
                </div>
              </div>
            </div>

            <div>
              <H2 className="mb-4">Send us a message</H2>
              <form className="space-y-4">
                <input
                  type="text"
                  placeholder="Your Name"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <input
                  type="email"
                  placeholder="Your Email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <textarea
                  placeholder="Your Message"
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <button
                  type="submit"
                  className="w-full bg-primary text-white py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Contact;
