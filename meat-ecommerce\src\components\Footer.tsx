import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Facebook, 
  Twitter, 
  Instagram, 
  Youtube,
  Phone,
  Mail,
  MapPin,
  Clock
} from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-secondary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">M</span>
              </div>
              <span className="font-secondary font-bold text-xl">MeatE</span>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              Your trusted partner for premium quality meat and seafood. 
              Fresh, hygienic, and delivered with care to your doorstep.
            </p>
            <div className="flex space-x-4">
              <a 
                href="#" 
                className="text-gray-300 hover:text-primary transition-colors duration-200"
                aria-label="Facebook"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a 
                href="#" 
                className="text-gray-300 hover:text-primary transition-colors duration-200"
                aria-label="Twitter"
              >
                <Twitter className="w-5 h-5" />
              </a>
              <a 
                href="#" 
                className="text-gray-300 hover:text-primary transition-colors duration-200"
                aria-label="Instagram"
              >
                <Instagram className="w-5 h-5" />
              </a>
              <a 
                href="#" 
                className="text-gray-300 hover:text-primary transition-colors duration-200"
                aria-label="YouTube"
              >
                <Youtube className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link 
                  to="/" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Home
                </Link>
              </li>
              <li>
                <Link 
                  to="/products" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Products
                </Link>
              </li>
              <li>
                <Link 
                  to="/about" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link 
                  to="/contact" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Contact
                </Link>
              </li>
              <li>
                <Link 
                  to="/privacy" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link 
                  to="/terms" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Terms & Conditions
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Categories</h3>
            <ul className="space-y-2">
              <li>
                <Link 
                  to="/products/chicken" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Chicken
                </Link>
              </li>
              <li>
                <Link 
                  to="/products/mutton" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Mutton
                </Link>
              </li>
              <li>
                <Link 
                  to="/products/fish" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Fish & Seafood
                </Link>
              </li>
              <li>
                <Link 
                  to="/products/ready-to-cook" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Ready to Cook
                </Link>
              </li>
              <li>
                <Link 
                  to="/products/marinades" 
                  className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Marinades
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Phone className="w-4 h-4 mt-1 text-primary flex-shrink-0" />
                <div>
                  <p className="text-gray-300 text-sm">+91 98765 43210</p>
                  <p className="text-gray-300 text-sm">+91 87654 32109</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Mail className="w-4 h-4 mt-1 text-primary flex-shrink-0" />
                <p className="text-gray-300 text-sm"><EMAIL></p>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="w-4 h-4 mt-1 text-primary flex-shrink-0" />
                <p className="text-gray-300 text-sm">
                  123 Meat Street, Fresh Market,<br />
                  Mumbai, Maharashtra 400001
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <Clock className="w-4 h-4 mt-1 text-primary flex-shrink-0" />
                <div>
                  <p className="text-gray-300 text-sm">Mon - Sat: 8:00 AM - 10:00 PM</p>
                  <p className="text-gray-300 text-sm">Sunday: 9:00 AM - 9:00 PM</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-300 text-sm">
            © {currentYear} MeatE-Commerce. All rights reserved.
          </p>
          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <span className="text-gray-300 text-sm">We Accept:</span>
            <div className="flex space-x-2">
              <div className="bg-white rounded px-2 py-1">
                <span className="text-xs font-semibold text-gray-800">VISA</span>
              </div>
              <div className="bg-white rounded px-2 py-1">
                <span className="text-xs font-semibold text-gray-800">MC</span>
              </div>
              <div className="bg-white rounded px-2 py-1">
                <span className="text-xs font-semibold text-gray-800">UPI</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
