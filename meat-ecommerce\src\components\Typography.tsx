import React from 'react';
import { cn } from '../utils/cn';

interface TypographyProps {
  children: React.ReactNode;
  className?: string;
  as?: React.ElementType;
}

// H1 Component - Merriweather, 700 weight
export const H1: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'h1' 
}) => (
  <Component 
    className={cn(
      'font-secondary font-bold text-h1-mobile md:text-h1-desktop text-secondary',
      className
    )}
  >
    {children}
  </Component>
);

// H2 Component - Inter, 600 weight
export const H2: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'h2' 
}) => (
  <Component 
    className={cn(
      'font-primary font-semibold text-h2-mobile md:text-h2-desktop text-secondary',
      className
    )}
  >
    {children}
  </Component>
);

// H3 Component - Inter, 500 weight
export const H3: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'h3' 
}) => (
  <Component 
    className={cn(
      'font-primary font-medium text-h3-mobile md:text-h3-desktop text-secondary',
      className
    )}
  >
    {children}
  </Component>
);

// Body Component - Inter, 400 weight
export const Body: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'p' 
}) => (
  <Component 
    className={cn(
      'font-primary font-normal text-body-mobile md:text-body-desktop text-secondary',
      className
    )}
  >
    {children}
  </Component>
);

// Caption Component - Inter, 400 weight, 12px
export const Caption: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'span' 
}) => (
  <Component 
    className={cn(
      'font-primary font-normal text-caption text-gray-600',
      className
    )}
  >
    {children}
  </Component>
);

// Export all components as a single Typography object
export const Typography = {
  H1,
  H2,
  H3,
  Body,
  Caption,
};

export default Typography;
