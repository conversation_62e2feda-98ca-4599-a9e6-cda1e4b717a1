import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Star,
  Plus,
  Minus,
  Heart,
  Share2,
  ShoppingCart,
  Truck,
  Shield,
  Award,
  Clock,
  ChevronLeft,
  ChevronRight,
  Zap,
  Users,
  ThumbsUp,
  MessageCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { H1, H2, H3, Body } from '../../components/Typography';
import { products } from '../../data/productsData';
import { useCartStore } from '../../stores/cartStore';
import { cn } from '../../utils/cn';
import { formatPrice } from '../../utils/formatPrice';
import ProductCard from '../../components/ProductCard';

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const product = products.find(p => p.id === id);
  const { addItem, removeItem, getItemQuantity } = useCartStore();

  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'description' | 'nutrition' | 'reviews'>('description');
  const [quantity, setQuantity] = useState(1);

  const cartQuantity = getItemQuantity(product?.id || '');

  // Enhanced product images
  const productImages = [
    'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1607623814075-e51df1bdc82f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1603048297172-c92544798d5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1551515120301-8c75c0b9b4c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  ];

  // Mock reviews data
  const reviews = [
    {
      id: 1,
      name: 'Rajesh Kumar',
      rating: 5,
      date: '2024-01-15',
      comment: 'Excellent quality meat! Fresh and tender. Perfect for BBQ. Will definitely order again.',
      helpful: 12,
      verified: true
    },
    {
      id: 2,
      name: 'Priya Sharma',
      rating: 4,
      date: '2024-01-10',
      comment: 'Good quality but delivery was slightly delayed. Meat was fresh and tasty though.',
      helpful: 8,
      verified: true
    },
    {
      id: 3,
      name: 'Mohammed Ali',
      rating: 5,
      date: '2024-01-08',
      comment: 'Outstanding! The marbling is perfect and the taste is incredible. Premium quality as promised.',
      helpful: 15,
      verified: true
    }
  ];

  // Mock nutrition data
  const nutritionData = {
    calories: 250,
    protein: 26,
    fat: 15,
    carbs: 0,
    fiber: 0,
    sodium: 75,
    iron: 15,
    vitaminB12: 89
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [id]);

  if (!product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center bg-white p-12 rounded-3xl shadow-xl max-w-md mx-4"
        >
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Info className="w-10 h-10 text-red-500" />
          </div>
          <H2 className="mb-4 text-gray-800">Product Not Found</H2>
          <Body className="text-gray-600 mb-8">The product you're looking for doesn't exist or has been removed.</Body>
          <Link
            to="/products"
            className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary/90 transition-colors"
          >
            <ChevronLeft className="w-4 h-4" />
            Browse Products
          </Link>
        </motion.div>
      </div>
    );
  }



  const handleAddToCart = () => {
    for (let i = 0; i < quantity; i++) {
      addItem(product);
    }
  };

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted);
  };

  const relatedProducts = products.filter(p =>
    p.category === product.category && p.id !== product.id
  ).slice(0, 4);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-gray-500 hover:text-primary transition-colors">Home</Link>
            <ChevronRight className="w-4 h-4 text-gray-400" />
            <Link to="/products" className="text-gray-500 hover:text-primary transition-colors">Products</Link>
            <ChevronRight className="w-4 h-4 text-gray-400" />
            <span className="text-gray-900 font-medium">{product.name}</span>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Product Images */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-6"
          >
            {/* Main Image */}
            <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl overflow-hidden group">
              <motion.img
                key={selectedImageIndex}
                initial={{ opacity: 0, scale: 1.1 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                src={productImages[selectedImageIndex]}
                alt={product.name}
                className="w-full h-96 lg:h-[500px] object-cover group-hover:scale-105 transition-transform duration-700"
                onError={(e) => {
                  const target = e.currentTarget;
                  target.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
                    <svg width="600" height="500" viewBox="0 0 600 500" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <defs>
                        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#F9FAFB"/>
                          <stop offset="100%" style="stop-color:#F3F4F6"/>
                        </linearGradient>
                      </defs>
                      <rect width="600" height="500" fill="url(#bg)"/>
                      <circle cx="300" cy="250" r="80" fill="#E62E04" opacity="0.8"/>
                      <text x="300" y="350" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#666" text-anchor="middle">${product.name}</text>
                      <text x="300" y="380" font-family="Arial, sans-serif" font-size="16" fill="#999" text-anchor="middle">Premium Quality Meat</text>
                    </svg>
                  `)}`;
                }}
              />

              {/* Image Navigation */}
              <div className="absolute inset-0 flex items-center justify-between p-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setSelectedImageIndex(prev => prev > 0 ? prev - 1 : productImages.length - 1)}
                  className="w-12 h-12 bg-white/90 backdrop-blur-md rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors"
                >
                  <ChevronLeft className="w-5 h-5 text-gray-700" />
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setSelectedImageIndex(prev => prev < productImages.length - 1 ? prev + 1 : 0)}
                  className="w-12 h-12 bg-white/90 backdrop-blur-md rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors"
                >
                  <ChevronRight className="w-5 h-5 text-gray-700" />
                </motion.button>
              </div>

              {/* Badges */}
              <div className="absolute top-6 left-6 flex flex-col gap-3">
                {product.discount && (
                  <motion.div
                    initial={{ scale: 0, rotate: -12 }}
                    animate={{ scale: 1, rotate: 0 }}
                    className="bg-gradient-to-r from-primary to-red-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg"
                  >
                    {product.discount}% OFF
                  </motion.div>
                )}

                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.1 }}
                  className="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg flex items-center gap-2"
                >
                  <CheckCircle className="w-4 h-4" />
                  FRESH
                </motion.div>
              </div>

              {/* Action Buttons */}
              <div className="absolute top-6 right-6 flex flex-col gap-3">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleWishlist}
                  className={cn(
                    "w-12 h-12 rounded-full backdrop-blur-md border border-white/20 flex items-center justify-center transition-all duration-200 shadow-lg",
                    isWishlisted
                      ? "bg-red-500 text-white"
                      : "bg-white/90 text-gray-600 hover:bg-white hover:text-red-500"
                  )}
                >
                  <Heart className={cn("w-5 h-5", isWishlisted && "fill-current")} />
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-12 h-12 rounded-full bg-white/90 backdrop-blur-md border border-white/20 text-gray-600 hover:text-primary flex items-center justify-center transition-all duration-200 shadow-lg"
                >
                  <Share2 className="w-5 h-5" />
                </motion.button>
              </div>
            </div>
            {/* Thumbnail Images */}
            <div className="flex gap-4 overflow-x-auto pb-2">
              {productImages.map((image, index) => (
                <motion.button
                  key={index}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setSelectedImageIndex(index)}
                  className={cn(
                    "flex-shrink-0 w-20 h-20 rounded-xl overflow-hidden border-2 transition-all duration-200",
                    selectedImageIndex === index
                      ? "border-primary shadow-lg"
                      : "border-gray-200 hover:border-gray-300"
                  )}
                >
                  <img
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.currentTarget;
                      target.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
                        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <rect width="80" height="80" fill="#F3F4F6"/>
                          <circle cx="40" cy="40" r="15" fill="#E62E04"/>
                        </svg>
                      `)}`;
                    }}
                  />
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Product Info */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-8"
          >
            {/* Category & Stock */}
            <div className="flex items-center justify-between">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="inline-flex items-center gap-2 bg-gradient-to-r from-primary/10 to-accent/10 text-primary px-4 py-2 rounded-full text-sm font-bold uppercase tracking-wide border border-primary/20"
              >
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                {product.category.replace('-', ' ')}
              </motion.div>

              <div className={cn(
                "px-4 py-2 rounded-full text-sm font-semibold border",
                product.inStock
                  ? "bg-green-50 text-green-700 border-green-200"
                  : "bg-red-50 text-red-700 border-red-200"
              )}>
                {product.inStock ? '✓ In Stock' : '✗ Out of Stock'}
              </div>
            </div>

            {/* Product Name & Rating */}
            <div className="space-y-4">
              <H1 className="text-4xl lg:text-5xl font-bold text-secondary leading-tight">
                {product.name}
              </H1>

              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2 bg-yellow-50 px-4 py-2 rounded-xl border border-yellow-200">
                  <div className="flex items-center gap-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={cn(
                          "w-5 h-5",
                          i < Math.floor(product.rating)
                            ? "text-yellow-500 fill-current"
                            : "text-gray-300"
                        )}
                      />
                    ))}
                  </div>
                  <span className="text-lg font-bold text-gray-800">{product.rating}</span>
                </div>

                <div className="text-gray-600">
                  <span className="font-semibold">{product.reviewCount}</span> reviews
                </div>

                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Users className="w-4 h-4" />
                  <span>50+ bought this week</span>
                </div>
              </div>
            </div>

            {/* Price Section */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 rounded-2xl border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-4">
                    <span className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
                      {formatPrice(product.price)}
                    </span>
                    {product.originalPrice && (
                      <span className="text-xl text-gray-500 line-through font-medium">
                        {formatPrice(product.originalPrice)}
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-600 font-medium">
                    per kg • Free delivery above ₹999
                  </div>
                </div>

                {product.discount && (
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-2xl text-center shadow-lg"
                  >
                    <div className="text-lg font-bold">Save</div>
                    <div className="text-sm">
                      {formatPrice((product.originalPrice || product.price) - product.price)}
                    </div>
                  </motion.div>
                )}
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="w-4 h-4 text-primary" />
                  <span className="font-medium">30-min delivery</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Shield className="w-4 h-4 text-primary" />
                  <span className="font-medium">100% Fresh</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Award className="w-4 h-4 text-primary" />
                  <span className="font-medium">Premium Quality</span>
                </div>
              </div>
            </div>

            {/* Product Details */}
            <div className="grid grid-cols-2 gap-6">
              <div className="bg-white p-4 rounded-xl border border-gray-200 shadow-sm">
                <div className="text-sm text-gray-600 mb-1">Weight</div>
                <div className="text-lg font-bold text-gray-800">{product.weight}</div>
              </div>

              {product.pieces && (
                <div className="bg-white p-4 rounded-xl border border-gray-200 shadow-sm">
                  <div className="text-sm text-gray-600 mb-1">Pieces</div>
                  <div className="text-lg font-bold text-gray-800">{product.pieces} pieces</div>
                </div>
              )}
            </div>

            {/* Quantity Selector */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <H3 className="text-xl font-bold text-gray-800">Quantity</H3>
                <div className="flex items-center gap-4">
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="w-12 h-12 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors"
                  >
                    <Minus className="w-5 h-5 text-gray-600" />
                  </motion.button>

                  <div className="w-16 h-12 bg-white border-2 border-gray-200 rounded-xl flex items-center justify-center">
                    <span className="text-xl font-bold text-gray-800">{quantity}</span>
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setQuantity(quantity + 1)}
                    className="w-12 h-12 bg-primary hover:bg-primary/90 rounded-full flex items-center justify-center transition-colors"
                  >
                    <Plus className="w-5 h-5 text-white" />
                  </motion.button>
                </div>
              </div>

              <div className="text-sm text-gray-600">
                Total: <span className="font-bold text-primary">{formatPrice(product.price * quantity)}</span>
              </div>
            </div>

            {/* Add to Cart Section */}
            <div className="space-y-4">
              {cartQuantity > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-green-50 border border-green-200 p-4 rounded-xl"
                >
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span className="text-green-700 font-medium">
                      {cartQuantity} item(s) already in cart
                    </span>
                  </div>
                </motion.div>
              )}

              <div className="flex gap-4">
                <motion.button
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleAddToCart}
                  disabled={!product.inStock}
                  className={cn(
                    "flex-1 flex items-center justify-center gap-3 py-4 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl",
                    product.inStock
                      ? "bg-gradient-to-r from-primary to-primary/90 text-white hover:from-primary/90 hover:to-primary"
                      : "bg-gray-200 text-gray-500 cursor-not-allowed shadow-none"
                  )}
                >
                  <ShoppingCart className="w-6 h-6" />
                  <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>
                  {product.inStock && <Zap className="w-6 h-6" />}
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 bg-white border-2 border-primary text-primary rounded-2xl font-bold hover:bg-primary hover:text-white transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Buy Now
                </motion.button>
              </div>
            </div>

            {/* Delivery Info */}
            <div className="bg-blue-50 border border-blue-200 p-6 rounded-2xl">
              <div className="flex items-center gap-3 mb-4">
                <Truck className="w-6 h-6 text-blue-600" />
                <H3 className="text-lg font-bold text-blue-800">Delivery Information</H3>
              </div>

              <div className="space-y-3 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-blue-700">Standard Delivery</span>
                  <span className="font-bold text-blue-800">30-45 minutes</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-700">Express Delivery</span>
                  <span className="font-bold text-blue-800">15-30 minutes</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-700">Free Delivery</span>
                  <span className="font-bold text-blue-800">Above ₹999</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Product Details Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-white rounded-3xl shadow-soft border border-gray-100 overflow-hidden"
        >
          {/* Tab Navigation */}
          <div className="flex border-b border-gray-200">
            {[
              { id: 'description', label: 'Description', icon: Info },
              { id: 'nutrition', label: 'Nutrition', icon: Award },
              { id: 'reviews', label: 'Reviews', icon: MessageCircle }
            ].map((tab) => (
              <motion.button
                key={tab.id}
                whileHover={{ y: -2 }}
                onClick={() => setSelectedTab(tab.id as any)}
                className={cn(
                  "flex-1 flex items-center justify-center gap-3 py-6 px-8 font-semibold transition-all duration-300",
                  selectedTab === tab.id
                    ? "bg-primary text-white shadow-lg"
                    : "text-gray-600 hover:text-primary hover:bg-gray-50"
                )}
              >
                <tab.icon className="w-5 h-5" />
                <span>{tab.label}</span>
              </motion.button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="p-8">
            <AnimatePresence mode="wait">
              {selectedTab === 'description' && (
                <motion.div
                  key="description"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <H2 className="text-2xl font-bold text-gray-800">Product Description</H2>
                  <div className="prose prose-lg max-w-none">
                    <p className="text-gray-700 leading-relaxed mb-6">
                      Experience the finest quality {product.name.toLowerCase()} sourced directly from premium farms.
                      Our meat is carefully selected, processed, and packaged to ensure maximum freshness and flavor.
                      Perfect for grilling, roasting, or any culinary adventure you have in mind.
                    </p>

                    <div className="grid md:grid-cols-2 gap-8">
                      <div>
                        <h4 className="text-lg font-bold text-gray-800 mb-4">Key Features</h4>
                        <ul className="space-y-3">
                          <li className="flex items-center gap-3">
                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                            <span className="text-gray-700">100% Fresh and Natural</span>
                          </li>
                          <li className="flex items-center gap-3">
                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                            <span className="text-gray-700">No Artificial Preservatives</span>
                          </li>
                          <li className="flex items-center gap-3">
                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                            <span className="text-gray-700">Ethically Sourced</span>
                          </li>
                          <li className="flex items-center gap-3">
                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                            <span className="text-gray-700">Premium Quality Cuts</span>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-lg font-bold text-gray-800 mb-4">Storage Instructions</h4>
                        <ul className="space-y-3">
                          <li className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="text-gray-700">Store in refrigerator at 0-4°C</span>
                          </li>
                          <li className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="text-gray-700">Use within 2-3 days of purchase</span>
                          </li>
                          <li className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="text-gray-700">Can be frozen for up to 3 months</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {selectedTab === 'nutrition' && (
                <motion.div
                  key="nutrition"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <H2 className="text-2xl font-bold text-gray-800">Nutrition Information</H2>
                  <p className="text-gray-600">Per 100g serving</p>

                  <div className="grid md:grid-cols-2 gap-8">
                    <div className="space-y-4">
                      {[
                        { label: 'Calories', value: nutritionData.calories, unit: 'kcal' },
                        { label: 'Protein', value: nutritionData.protein, unit: 'g' },
                        { label: 'Total Fat', value: nutritionData.fat, unit: 'g' },
                        { label: 'Carbohydrates', value: nutritionData.carbs, unit: 'g' }
                      ].map((item) => (
                        <div key={item.label} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                          <span className="font-medium text-gray-700">{item.label}</span>
                          <span className="font-bold text-gray-900">{item.value}{item.unit}</span>
                        </div>
                      ))}
                    </div>

                    <div className="space-y-4">
                      {[
                        { label: 'Dietary Fiber', value: nutritionData.fiber, unit: 'g' },
                        { label: 'Sodium', value: nutritionData.sodium, unit: 'mg' },
                        { label: 'Iron', value: nutritionData.iron, unit: '% DV' },
                        { label: 'Vitamin B12', value: nutritionData.vitaminB12, unit: '% DV' }
                      ].map((item) => (
                        <div key={item.label} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                          <span className="font-medium text-gray-700">{item.label}</span>
                          <span className="font-bold text-gray-900">{item.value}{item.unit}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 p-6 rounded-2xl">
                    <div className="flex items-center gap-3 mb-3">
                      <Info className="w-5 h-5 text-blue-600" />
                      <span className="font-bold text-blue-800">Health Benefits</span>
                    </div>
                    <p className="text-blue-700 text-sm">
                      Rich in high-quality protein, essential amino acids, iron, and vitamin B12.
                      Supports muscle growth, immune function, and overall health.
                    </p>
                  </div>
                </motion.div>
              )}

              {selectedTab === 'reviews' && (
                <motion.div
                  key="reviews"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-8"
                >
                  <div className="flex items-center justify-between">
                    <H2 className="text-2xl font-bold text-gray-800">Customer Reviews</H2>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Star className="w-5 h-5 text-yellow-500 fill-current" />
                        <span className="text-lg font-bold">{product.rating}</span>
                        <span className="text-gray-600">({product.reviewCount} reviews)</span>
                      </div>
                    </div>
                  </div>

                  {/* Rating Breakdown */}
                  <div className="bg-gray-50 p-6 rounded-2xl">
                    <div className="grid grid-cols-5 gap-4 mb-6">
                      {[5, 4, 3, 2, 1].map((rating) => (
                        <div key={rating} className="flex items-center gap-2">
                          <span className="text-sm font-medium">{rating}</span>
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-yellow-500 h-2 rounded-full"
                              style={{ width: `${rating === 5 ? 70 : rating === 4 ? 20 : 10}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-600">
                            {rating === 5 ? '70%' : rating === 4 ? '20%' : '10%'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Individual Reviews */}
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <motion.div
                        key={review.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-white border border-gray-200 p-6 rounded-2xl shadow-sm"
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center text-white font-bold">
                              {review.name.charAt(0)}
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <span className="font-bold text-gray-800">{review.name}</span>
                                {review.verified && (
                                  <div className="flex items-center gap-1 bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                                    <CheckCircle className="w-3 h-3" />
                                    Verified
                                  </div>
                                )}
                              </div>
                              <div className="flex items-center gap-2 mt-1">
                                <div className="flex items-center gap-1">
                                  {[...Array(5)].map((_, i) => (
                                    <Star
                                      key={i}
                                      className={cn(
                                        "w-4 h-4",
                                        i < review.rating
                                          ? "text-yellow-500 fill-current"
                                          : "text-gray-300"
                                      )}
                                    />
                                  ))}
                                </div>
                                <span className="text-sm text-gray-500">{review.date}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <p className="text-gray-700 mb-4 leading-relaxed">{review.comment}</p>

                        <div className="flex items-center gap-4">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="flex items-center gap-2 text-sm text-gray-600 hover:text-primary transition-colors"
                          >
                            <ThumbsUp className="w-4 h-4" />
                            <span>Helpful ({review.helpful})</span>
                          </motion.button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="space-y-8"
          >
            <div className="text-center">
              <H2 className="text-3xl font-bold text-gray-800 mb-4">You Might Also Like</H2>
              <Body className="text-gray-600 max-w-2xl mx-auto">
                Discover more premium quality products from our carefully curated selection
              </Body>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {relatedProducts.map((relatedProduct, index) => (
                <motion.div
                  key={relatedProduct.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                >
                  <ProductCard
                    product={relatedProduct}
                    variant="compact"
                    className="h-full"
                  />
                </motion.div>
              ))}
            </div>

            <div className="text-center">
              <Link
                to="/products"
                className="inline-flex items-center gap-3 bg-gradient-to-r from-primary to-primary/90 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-primary/90 hover:to-primary transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <span>View All Products</span>
                <ChevronRight className="w-5 h-5" />
              </Link>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ProductDetail;