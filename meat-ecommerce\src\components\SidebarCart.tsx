import React from 'react';
import { Link } from 'react-router-dom';
import { X, Plus, Minus, ShoppingBag, Trash2 } from 'lucide-react';
import { useCartStore } from '../stores/cartStore';
import { cn } from '../utils/cn';
import { formatPrice } from '../utils/formatPrice';

interface SidebarCartProps {
  isOpen: boolean;
  onClose: () => void;
}

const SidebarCart: React.FC<SidebarCartProps> = ({ isOpen, onClose }) => {
  const { items, addItem, removeItem, clearCart, getTotalPrice, getTotalItems } = useCartStore();



  const getProductImage = (productId: string) => {
    const baseImages = [
      'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
      'https://images.unsplash.com/photo-1607623814075-e51df1bdc82f?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
      'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
      'https://images.unsplash.com/photo-1603048297172-c92544798d5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
      'https://images.unsplash.com/photo-1551515120301-8c75c0b9b4c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80'
    ];
    const imageIndex = parseInt(productId) % baseImages.length;
    return baseImages[imageIndex];
  };

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed top-0 left-0 h-full w-96 bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center gap-3">
              <ShoppingBag className="w-6 h-6 text-primary" />
              <h2 className="text-xl font-bold text-gray-800">Your Cart</h2>
              <span className="bg-primary text-white text-sm px-2 py-1 rounded-full">
                {getTotalItems()}
              </span>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-lg bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors duration-200"
            >
              <X className="w-5 h-5 text-gray-600" />
            </button>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-4">
            {items.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <ShoppingBag className="w-16 h-16 text-gray-300 mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">Your cart is empty</h3>
                <p className="text-gray-500 mb-6">Add some delicious items to get started!</p>
                <Link
                  to="/products"
                  onClick={onClose}
                  className="bg-primary text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary/90 transition-colors duration-200"
                >
                  Browse Products
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {items.map((item) => (
                  <div key={item.id} className="bg-gray-50 rounded-xl p-4 border border-gray-100">
                    <div className="flex gap-4">
                      {/* Product Image */}
                      <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0">
                        <img
                          src={getProductImage(item.id)}
                          alt={item.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.currentTarget;
                            target.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
                              <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="64" height="64" fill="#F3F4F6"/>
                                <circle cx="32" cy="32" r="16" fill="#E62E04" opacity="0.8"/>
                              </svg>
                            `)}`;
                          }}
                        />
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-gray-800 text-sm line-clamp-2 mb-1">
                          {item.name}
                        </h4>
                        <p className="text-xs text-gray-500 mb-2">{item.weight}</p>
                        
                        <div className="flex items-center justify-between">
                          <div className="text-sm font-bold text-primary">
                            {formatPrice(item.price)}
                          </div>
                          
                          {/* Quantity Controls */}
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => removeItem(item.id)}
                              className="w-7 h-7 rounded-lg bg-white border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors duration-200"
                            >
                              <Minus className="w-3 h-3 text-gray-600" />
                            </button>
                            
                            <span className="w-8 text-center text-sm font-semibold text-gray-800">
                              {item.quantity}
                            </span>
                            
                            <button
                              onClick={() => addItem(item)}
                              className="w-7 h-7 rounded-lg bg-primary text-white flex items-center justify-center hover:bg-primary/90 transition-colors duration-200"
                            >
                              <Plus className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                        
                        {/* Item Total */}
                        <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-200">
                          <span className="text-xs text-gray-500">Total:</span>
                          <span className="text-sm font-bold text-gray-800">
                            {formatPrice(item.price * item.quantity)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Clear Cart Button */}
                <button
                  onClick={clearCart}
                  className="w-full flex items-center justify-center gap-2 py-3 text-red-600 hover:bg-red-50 rounded-xl transition-colors duration-200 border border-red-200"
                >
                  <Trash2 className="w-4 h-4" />
                  <span className="font-semibold">Clear Cart</span>
                </button>
              </div>
            )}
          </div>

          {/* Footer */}
          {items.length > 0 && (
            <div className="border-t border-gray-200 p-4 bg-gray-50">
              {/* Delivery Info */}
              <div className="mb-4 p-3 bg-blue-50 rounded-xl border border-blue-200">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-blue-700">Delivery:</span>
                  <span className="font-semibold text-blue-800">
                    {getTotalPrice() >= 999 ? 'FREE' : '₹40'}
                  </span>
                </div>
                {getTotalPrice() < 999 && (
                  <p className="text-xs text-blue-600 mt-1">
                    Add ₹{999 - getTotalPrice()} more for free delivery
                  </p>
                )}
              </div>

              {/* Total */}
              <div className="flex items-center justify-between mb-4 p-3 bg-white rounded-xl border border-gray-200">
                <span className="text-lg font-bold text-gray-800">Total:</span>
                <span className="text-xl font-bold text-primary">
                  {formatPrice(getTotalPrice() + (getTotalPrice() >= 999 ? 0 : 40))}
                </span>
              </div>

              {/* Checkout Buttons */}
              <div className="space-y-3">
                <Link
                  to="/checkout"
                  onClick={onClose}
                  className="w-full bg-primary text-white py-4 rounded-xl font-bold text-center hover:bg-primary/90 transition-colors duration-200 flex items-center justify-center gap-2"
                >
                  <ShoppingBag className="w-5 h-5" />
                  Place Order
                </Link>
                
                <Link
                  to="/cart"
                  onClick={onClose}
                  className="w-full border-2 border-primary text-primary py-3 rounded-xl font-semibold text-center hover:bg-primary hover:text-white transition-all duration-200"
                >
                  View Cart Details
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default SidebarCart;
