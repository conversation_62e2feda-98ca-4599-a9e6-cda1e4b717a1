import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  Plus,
  Minus,
  X,
  ShoppingBag,
  Trash2,
  Heart,
  ArrowLeft,
  Truck,
  Shield,
  Clock,
  Tag,
  Gift
} from 'lucide-react';
import { H1, H2, H3, Body } from '../components/Typography';
import { useCartStore } from '../stores/cartStore';
import { formatPrice } from '../utils/formatPrice';
import { cn } from '../utils/cn';

const Cart: React.FC = () => {
  const {
    items,
    itemCount,
    getSubtotal,
    getTax,
    getTotal,
    updateQuantity,
    removeItem,
    clearCart
  } = useCartStore();

  const [couponCode, setCouponCode] = useState('');
  const [appliedCoupon, setAppliedCoupon] = useState<string | null>(null);
  const [couponDiscount, setCouponDiscount] = useState(0);

  const deliveryFee = getSubtotal() >= 999 ? 0 : 40;
  const finalTotal = getTotal() + deliveryFee - couponDiscount;

  const applyCoupon = () => {
    // Mock coupon logic
    const validCoupons = {
      'SAVE10': 10,
      'WELCOME20': 20,
      'MEAT50': 50
    };

    if (validCoupons[couponCode as keyof typeof validCoupons]) {
      const discount = validCoupons[couponCode as keyof typeof validCoupons];
      setAppliedCoupon(couponCode);
      setCouponDiscount(discount);
      setCouponCode('');
    }
  };

  const removeCoupon = () => {
    setAppliedCoupon(null);
    setCouponDiscount(0);
  };

  if (itemCount === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
            <ShoppingBag className="w-16 h-16 text-gray-400" />
          </div>
          <H1 className="mb-4 text-gray-800">Your cart is empty</H1>
          <Body className="text-gray-600 mb-8 leading-relaxed">
            Looks like you haven't added any delicious meat products yet.
            Start exploring our fresh collection!
          </Body>
          <div className="space-y-4">
            <Link
              to="/products"
              className="inline-flex items-center gap-2 bg-primary text-white px-8 py-4 rounded-xl font-semibold hover:bg-primary/90 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <ShoppingBag className="w-5 h-5" />
              Start Shopping
            </Link>
            <div className="flex items-center justify-center gap-6 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <Truck className="w-4 h-4" />
                <span>Free Delivery</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                <span>Fresh Quality</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link
              to="/products"
              className="flex items-center gap-2 text-gray-600 hover:text-primary transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Continue Shopping</span>
            </Link>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <H1 className="mb-2">Shopping Cart</H1>
              <Body className="text-gray-600">
                {itemCount} {itemCount === 1 ? 'item' : 'items'} in your cart
              </Body>
            </div>
            {itemCount > 0 && (
              <button
                onClick={clearCart}
                className="flex items-center gap-2 text-sm text-red-600 hover:text-red-700 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                Clear Cart
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <H3>Order Items</H3>
              </div>
              <div className="divide-y divide-gray-100">
                {items.map((item) => (
                  <div key={item.product.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start gap-4">
                      {/* Product Image */}
                      <div className="relative">
                        <img
                          src={item.product.image}
                          alt={item.product.name}
                          className="w-24 h-24 object-cover rounded-xl"
                        />
                        <button
                          onClick={() => removeItem(item.product.id)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>

                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <H3 className="font-semibold text-gray-900 mb-1">{item.product.name}</H3>
                            <Body className="text-gray-600 text-sm">{item.product.weight}</Body>
                          </div>
                          <button className="text-gray-400 hover:text-red-500 transition-colors">
                            <Heart className="w-5 h-5" />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          {/* Price */}
                          <div className="text-lg font-bold text-primary">
                            {formatPrice(item.product.price)}
                            <span className="text-sm text-gray-500 font-normal"> per kg</span>
                          </div>

                          {/* Quantity Controls */}
                          <div className="flex items-center gap-3">
                            <div className="flex items-center border border-gray-200 rounded-lg">
                              <button
                                onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                                className="p-2 text-gray-500 hover:text-primary hover:bg-gray-50 transition-colors rounded-l-lg"
                              >
                                <Minus className="w-4 h-4" />
                              </button>
                              <span className="px-4 py-2 font-semibold text-gray-900 min-w-[3rem] text-center">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                                className="p-2 text-gray-500 hover:text-primary hover:bg-gray-50 transition-colors rounded-r-lg"
                              >
                                <Plus className="w-4 h-4" />
                              </button>
                            </div>

                            <div className="text-right">
                              <div className="text-lg font-bold text-gray-900">
                                {formatPrice(item.product.price * item.quantity)}
                              </div>
                              <div className="text-xs text-gray-500">
                                Total
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-sm sticky top-6">
              <div className="p-6 border-b border-gray-100">
                <H3>Order Summary</H3>
              </div>

              <div className="p-6 space-y-4">
                {/* Delivery Info */}
                <div className="bg-blue-50 p-4 rounded-xl border border-blue-100">
                  <div className="flex items-center gap-3 mb-2">
                    <Truck className="w-5 h-5 text-blue-600" />
                    <span className="font-semibold text-blue-800">Delivery Information</span>
                  </div>
                  <div className="text-sm text-blue-700">
                    {deliveryFee === 0 ? (
                      <div className="flex items-center gap-2">
                        <span className="font-semibold">FREE Delivery</span>
                        <span className="text-green-600">✓</span>
                      </div>
                    ) : (
                      <div>
                        <div>Delivery Fee: {formatPrice(deliveryFee)}</div>
                        <div className="text-xs mt-1">
                          Add {formatPrice(999 - getSubtotal())} more for free delivery
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Coupon Section */}
                <div className="border border-gray-200 rounded-xl p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Tag className="w-4 h-4 text-gray-600" />
                    <span className="font-semibold text-gray-800">Coupon Code</span>
                  </div>

                  {appliedCoupon ? (
                    <div className="flex items-center justify-between bg-green-50 p-3 rounded-lg border border-green-200">
                      <div className="flex items-center gap-2">
                        <Gift className="w-4 h-4 text-green-600" />
                        <span className="text-green-800 font-semibold">{appliedCoupon}</span>
                      </div>
                      <button
                        onClick={removeCoupon}
                        className="text-red-600 hover:text-red-700 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                        placeholder="Enter coupon code"
                        className="flex-1 px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                      />
                      <button
                        onClick={applyCoupon}
                        className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-semibold hover:bg-gray-200 transition-colors"
                      >
                        Apply
                      </button>
                    </div>
                  )}
                </div>

                {/* Price Breakdown */}
                <div className="space-y-3 pt-4 border-t border-gray-100">
                  <div className="flex justify-between text-gray-600">
                    <span>Subtotal ({itemCount} items)</span>
                    <span>{formatPrice(getSubtotal())}</span>
                  </div>
                  <div className="flex justify-between text-gray-600">
                    <span>Tax (18%)</span>
                    <span>{formatPrice(getTax())}</span>
                  </div>
                  <div className="flex justify-between text-gray-600">
                    <span>Delivery Fee</span>
                    <span>{deliveryFee === 0 ? 'FREE' : formatPrice(deliveryFee)}</span>
                  </div>
                  {couponDiscount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Coupon Discount</span>
                      <span>-{formatPrice(couponDiscount)}</span>
                    </div>
                  )}
                  <div className="flex justify-between text-xl font-bold text-gray-900 pt-3 border-t border-gray-200">
                    <span>Total</span>
                    <span>{formatPrice(finalTotal)}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3 pt-4">
                  <Link
                    to="/checkout"
                    className="w-full bg-primary text-white py-4 rounded-xl font-semibold hover:bg-primary/90 transition-colors text-center block shadow-lg hover:shadow-xl"
                  >
                    Proceed to Checkout
                  </Link>

                  <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Shield className="w-4 h-4" />
                      <span>Secure Payment</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>30min Delivery</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
