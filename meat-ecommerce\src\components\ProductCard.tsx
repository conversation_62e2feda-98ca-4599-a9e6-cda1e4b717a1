import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Star, Plus, Minus, Heart, ShoppingCart } from 'lucide-react';
import type { Product } from '../types/index';
import { useCartStore } from '../stores/cartStore';
import { cn } from '../utils/cn';
import { formatPrice } from '../utils/formatPrice';

interface ProductCardProps {
  product: Product;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  className,
  variant = 'default'
}) => {
  const { addItem, removeItem, getItemQuantity } = useCartStore();
  const quantity = getItemQuantity(product.id);
  const [isWishlisted, setIsWishlisted] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addItem(product);
  };

  const handleRemoveFromCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    removeItem(product.id);
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
  };



  const getProductImages = () => {
    const baseImages = [
      'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      'https://images.unsplash.com/photo-1607623814075-e51df1bdc82f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      'https://images.unsplash.com/photo-1603048297172-c92544798d5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      'https://images.unsplash.com/photo-1551515120301-8c75c0b9b4c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
    ];

    // Use product ID to consistently select an image
    const imageIndex = parseInt(product.id) % baseImages.length;
    return baseImages[imageIndex];
  };

  return (
    <div
      className={cn(
        "group relative bg-white rounded-2xl shadow-sm hover:shadow-lg transition-shadow duration-200 overflow-hidden border border-gray-100",
        variant === 'compact' ? 'p-3' : variant === 'featured' ? 'p-6' : 'p-4',
        "hover:-translate-y-1 transition-transform duration-200",
        className
      )}
    >
      <Link to={`/product/${product.id}`} className="block">
        {/* Product Image Container */}
        <div className="relative mb-4 overflow-hidden rounded-xl bg-gray-50">
          <img
            src={getProductImages()}
            alt={product.name}
            className={cn(
              "w-full object-cover transition-transform duration-200 group-hover:scale-105",
              variant === 'compact' ? 'h-32' : variant === 'featured' ? 'h-48' : 'h-40'
            )}
            onError={(e) => {
              const target = e.currentTarget;
              target.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
                <svg width="300" height="200" viewBox="0 0 300 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="300" height="200" fill="#F3F4F6"/>
                  <circle cx="150" cy="100" r="30" fill="#E62E04" opacity="0.8"/>
                  <text x="150" y="140" font-family="Arial, sans-serif" font-size="12" font-weight="600" fill="#666" text-anchor="middle">${product.name}</text>
                </svg>
              `)}`;
            }}
          />

          {/* Top Badges */}
          <div className="absolute top-3 left-3 flex flex-col gap-2">
            {product.discount && (
              <div className="bg-red-500 text-white px-2 py-1 rounded-lg text-xs font-bold">
                {product.discount}% OFF
              </div>
            )}
          </div>

          {/* Top Right Actions */}
          <div className="absolute top-3 right-3">
            <button
              onClick={handleWishlist}
              className={cn(
                "w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm border border-white/20 flex items-center justify-center transition-colors duration-200 shadow-sm",
                isWishlisted
                  ? "text-red-500"
                  : "text-gray-600 hover:text-red-500"
              )}
            >
              <Heart className={cn("w-4 h-4", isWishlisted && "fill-current")} />
            </button>
          </div>

          {/* Stock Status */}
          <div className="absolute bottom-3 left-3">
            <div className={cn(
              "px-2 py-1 rounded-lg text-xs font-semibold",
              product.inStock
                ? "bg-green-100 text-green-700"
                : "bg-red-100 text-red-700"
            )}>
              {product.inStock ? 'In Stock' : 'Out of Stock'}
            </div>
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-3">
          {/* Category Badge */}
          <div className="flex items-center justify-between">
            <div className="inline-flex items-center gap-1 bg-primary/10 text-primary px-2 py-1 rounded-lg text-xs font-semibold">
              {product.category.replace('-', ' ')}
            </div>
          </div>

          {/* Product Name */}
          <div>
            <h3 className={cn(
              "font-bold text-gray-800 line-clamp-2 group-hover:text-primary transition-colors duration-200",
              variant === 'compact' ? 'text-sm' : variant === 'featured' ? 'text-lg' : 'text-base'
            )}>
              {product.name}
            </h3>

            {variant === 'featured' && (
              <p className="text-gray-600 text-sm mt-2 line-clamp-2">
                Premium quality meat sourced from trusted farms. Perfect for grilling, roasting, and special occasions.
              </p>
            )}
          </div>

          {/* Weight and Pieces */}
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2 bg-gray-50 px-3 py-2 rounded-lg">
              <span className="font-semibold text-gray-700">{product.weight}</span>
            </div>
            {product.pieces && (
              <div className="flex items-center gap-2 bg-gray-50 px-3 py-2 rounded-lg">
                <span className="font-semibold text-gray-700">{product.pieces} pieces</span>
              </div>
            )}
          </div>

          {/* Enhanced Rating */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1 bg-yellow-50 px-3 py-2 rounded-lg border border-yellow-200">
                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                <span className="text-sm font-bold text-gray-800">{product.rating}</span>
              </div>
              <span className="text-xs text-gray-500 font-medium">
                ({product.reviewCount} reviews)
              </span>
            </div>

            {variant === 'featured' && (
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Clock className="w-3 h-3" />
                <span>Fresh today</span>
              </div>
            )}
          </div>

          {/* Price Section */}
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-2">
                <span className={cn(
                  "font-bold text-primary",
                  variant === 'compact' ? 'text-lg' : variant === 'featured' ? 'text-xl' : 'text-lg'
                )}>
                  {formatPrice(product.price)}
                </span>
                {product.originalPrice && (
                  <span className="text-sm text-gray-500 line-through">
                    {formatPrice(product.originalPrice)}
                  </span>
                )}
              </div>
              <div className="text-xs text-gray-600">per kg</div>
            </div>

            {product.discount && (
              <div className="bg-green-500 text-white px-2 py-1 rounded-lg text-xs font-bold">
                {product.discount}% OFF
              </div>
            )}
          </div>

          {/* Rating */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
              <span className="text-sm font-semibold">{product.rating}</span>
            </div>
            <span className="text-xs text-gray-500">({product.reviewCount})</span>
          </div>
        </div>
      </Link>

      {/* Cart Controls */}
      <div className="mt-4">
        {quantity > 0 ? (
          <div className="flex items-center justify-between bg-green-50 p-3 rounded-xl border border-green-200">
            <div className="flex items-center gap-3">
              <button
                onClick={handleRemoveFromCart}
                className="w-8 h-8 bg-white text-green-600 rounded-lg flex items-center justify-center border border-green-200 hover:bg-green-50 transition-colors duration-200"
              >
                <Minus className="w-4 h-4" />
              </button>

              <div className="text-center">
                <div className="text-sm font-bold text-green-700">{quantity}</div>
              </div>

              <button
                onClick={handleAddToCart}
                disabled={!product.inStock}
                className="w-8 h-8 bg-green-600 text-white rounded-lg flex items-center justify-center hover:bg-green-700 transition-colors duration-200 disabled:opacity-50"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>

            <div className="text-right">
              <div className="text-sm font-bold text-green-700">
                {formatPrice(product.price * quantity)}
              </div>
              <div className="text-xs text-green-600">Total</div>
            </div>
          </div>
        ) : (
          <button
            onClick={handleAddToCart}
            disabled={!product.inStock}
            className={cn(
              "w-full flex items-center justify-center gap-2 py-3 rounded-xl font-semibold transition-colors duration-200",
              product.inStock
                ? "bg-primary text-white hover:bg-primary/90"
                : "bg-gray-200 text-gray-500 cursor-not-allowed"
            )}
          >
            <ShoppingCart className="w-4 h-4" />
            <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default ProductCard;
