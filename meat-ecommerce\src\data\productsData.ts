import type { Product, Category } from '../types/index';

export const categories: Category[] = [
  {
    id: '1',
    name: 'Chicken',
    slug: 'chicken',
    icon: '🐔',
    image: '/images/categories/chicken.jpg',
    description: 'Fresh chicken cuts and whole chicken',
    productCount: 12,
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    slug: 'mutton',
    icon: '🐑',
    image: '/images/categories/mutton.jpg',
    description: 'Premium mutton cuts and goat meat',
    productCount: 8,
  },
  {
    id: '3',
    name: 'Fish & Seafood',
    slug: 'fish',
    icon: '🐟',
    image: '/images/categories/fish.jpg',
    description: 'Fresh fish and seafood varieties',
    productCount: 15,
  },
  {
    id: '4',
    name: 'Ready to Cook',
    slug: 'ready-to-cook',
    icon: '🍳',
    image: '/images/categories/ready-to-cook.jpg',
    description: 'Marinated and ready-to-cook items',
    productCount: 10,
  },
  {
    id: '5',
    name: '<PERSON><PERSON>',
    slug: 'marinades',
    icon: '🧂',
    image: '/images/categories/marinades.jpg',
    description: 'Spices and marinades for meat',
    productCount: 6,
  },
];

export const products: Product[] = [
  {
    id: '1',
    name: 'Fresh Chicken Breast',
    description: 'Boneless chicken breast, perfect for grilling and roasting',
    price: 299,
    originalPrice: 349,
    discount: 14,
    image: '/images/products/chicken-breast.jpg',
    images: [
      '/images/products/chicken-breast.jpg',
      '/images/products/chicken-breast-2.jpg',
    ],
    category: 'chicken',
    weight: '500g',
    pieces: 2,
    rating: 4.5,
    reviewCount: 128,
    inStock: true,
    nutrition: {
      calories: 165,
      protein: 31,
      fat: 3.6,
      carbs: 0,
    },
    tags: ['boneless', 'fresh', 'protein-rich'],
  },
  {
    id: '2',
    name: 'Chicken Drumsticks',
    description: 'Fresh chicken drumsticks, great for BBQ and curry',
    price: 249,
    originalPrice: 279,
    discount: 11,
    image: '/images/products/chicken-drumsticks.jpg',
    category: 'chicken',
    weight: '500g',
    pieces: 6,
    rating: 4.3,
    reviewCount: 95,
    inStock: true,
    nutrition: {
      calories: 172,
      protein: 28,
      fat: 5.7,
      carbs: 0,
    },
    tags: ['bone-in', 'fresh', 'bbq-ready'],
  },
  {
    id: '3',
    name: 'Mutton Shoulder',
    description: 'Premium mutton shoulder cuts, ideal for slow cooking',
    price: 699,
    originalPrice: 799,
    discount: 13,
    image: '/images/products/mutton-shoulder.jpg',
    category: 'mutton',
    weight: '500g',
    rating: 4.7,
    reviewCount: 67,
    inStock: true,
    nutrition: {
      calories: 294,
      protein: 25,
      fat: 21,
      carbs: 0,
    },
    tags: ['premium', 'tender', 'slow-cook'],
  },
  {
    id: '4',
    name: 'Fresh Pomfret',
    description: 'Whole fresh pomfret, cleaned and ready to cook',
    price: 449,
    image: '/images/products/pomfret.jpg',
    category: 'fish',
    weight: '400g',
    pieces: 1,
    rating: 4.4,
    reviewCount: 89,
    inStock: true,
    nutrition: {
      calories: 96,
      protein: 19,
      fat: 1.2,
      carbs: 0,
    },
    tags: ['whole-fish', 'cleaned', 'omega-3'],
  },
  {
    id: '5',
    name: 'Chicken Tikka (Marinated)',
    description: 'Pre-marinated chicken tikka, ready to grill',
    price: 349,
    originalPrice: 399,
    discount: 13,
    image: '/images/products/chicken-tikka.jpg',
    category: 'ready-to-cook',
    weight: '400g',
    pieces: 8,
    rating: 4.6,
    reviewCount: 156,
    inStock: true,
    nutrition: {
      calories: 185,
      protein: 29,
      fat: 6.2,
      carbs: 2.1,
    },
    tags: ['marinated', 'ready-to-cook', 'spicy'],
  },
  {
    id: '6',
    name: 'Prawns Large',
    description: 'Fresh large prawns, deveined and cleaned',
    price: 599,
    originalPrice: 649,
    discount: 8,
    image: '/images/products/prawns.jpg',
    category: 'seafood',
    weight: '250g',
    pieces: 12,
    rating: 4.5,
    reviewCount: 73,
    inStock: true,
    nutrition: {
      calories: 99,
      protein: 24,
      fat: 0.3,
      carbs: 0.2,
    },
    tags: ['deveined', 'cleaned', 'large-size'],
  },
];

export const bestSellers = products.filter(product => 
  ['1', '3', '5', '6'].includes(product.id)
);

export const featuredProducts = products.slice(0, 4);
