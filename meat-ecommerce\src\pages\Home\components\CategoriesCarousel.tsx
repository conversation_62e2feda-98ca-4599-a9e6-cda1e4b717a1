import React from 'react';
import { Link } from 'react-router-dom';
import { H2, Body } from '../../../components/Typography';
import { categories } from '../../../data/productsData';

const CategoriesCarousel: React.FC = () => {
  // Category images mapping
  const getCategoryImage = (categorySlug: string) => {
    const imageMap: Record<string, string> = {
      'chicken': 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      'mutton': 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      'fish': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      'prawns': 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      'eggs': 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    };
    return imageMap[categorySlug] || imageMap['chicken'];
  };

  return (
    <section className="py-12 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-10">
          <H2 className="mb-3">Shop by Categories</H2>
          <Body className="text-gray-600 max-w-2xl mx-auto">
            Freshest meats and much more!
          </Body>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {categories.map((category) => (
            <Link
              key={category.id}
              to={`/products/${category.slug}`}
              className="group block text-center"
            >
              <div className="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-1">
                {/* Category Image */}
                <div className="w-20 h-20 mx-auto mb-3 rounded-xl overflow-hidden bg-gray-100">
                  <img
                    src={getCategoryImage(category.slug)}
                    alt={category.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-200"
                    onError={(e) => {
                      const target = e.currentTarget;
                      target.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
                        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <rect width="80" height="80" fill="#F3F4F6"/>
                          <circle cx="40" cy="40" r="20" fill="#E62E04" opacity="0.8"/>
                          <text x="40" y="45" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle">${category.icon}</text>
                        </svg>
                      `)}`;
                    }}
                  />
                </div>

                {/* Category Name */}
                <h3 className="font-semibold text-gray-800 group-hover:text-primary transition-colors duration-200 mb-1 text-sm">
                  {category.name}
                </h3>

                {/* Product Count */}
                <p className="text-xs text-gray-500">
                  {category.productCount} items
                </p>
              </div>
            </Link>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-8">
          <Link
            to="/products"
            className="inline-flex items-center px-6 py-3 border-2 border-primary text-primary font-semibold rounded-xl hover:bg-primary hover:text-white transition-all duration-200"
          >
            View All Products
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CategoriesCarousel;
